export const chatLoadingTips = [
  "您可以尝试用[ER图出题]生成练习并在ER模块绘制~",
  "智能体正在努力生成回复，请不要着急...",
  "您可以在[题目历史]模块查看已生成的题目~",
  "出错了?请联系工作人员...",
  "可以尝试将ER题目描述固定在画布上,方便同时编辑属性和节点",
  "树协议可以保证冲突可序列化并排除死锁,但是不能保证无级联和可恢复性",
  "直接建立在文件系统上的数据库应用导致数据冗余、数据孤岛等问题",
  "数据字典：DDL compiler generates a set of table templates stored in a data dictionary",
  "参照完整性：要求参照表中的外键属性一定在被参照表中的至少一个元组中存在",
  "外连接：在自然连接的基础上，保留特定关系不匹配的元组",
  "domain可以理解为对数据类型的扩展定义，它允许我们创建自定义的数据类型，并且可以为其指定约束条件。",
  "两阶段锁协议可以保证冲突可序列化,但是冲突可序列化的调度不一定满足2PL协议",
  "树协议是图协议的一种，适用于对数据访问的次序具有偏序结构理解的情况",
  "设计范式的时候，主要避免两个问题：1. redundancy 2. incompleteness",
  "按照BCNF分解,一定是无损分解,但不一定满足依赖保留",
  "稠密索引——对建立索引的属性，每一个值都有自己的指针",
];
