.canvasContainer {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fafafa;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 拖拽悬浮状态 */
.canvasContainer.dragOver {
  background: rgba(22, 119, 255, 0.05);
  border: 2px dashed #1677ff;
}

/* 当包含 ERDiagram 组件时的样式 */
.canvasContainer > div:first-child:not(.canvasContent) {
  width: 100%;
  height: 100%;
}

/* 拖放覆盖层 */
.dropOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(22, 119, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  pointer-events: none;
}

.dropMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid #1677ff;
}

.dropIcon {
  font-size: 48px;
  color: #1677ff;
}

.dropMessage span {
  font-size: 16px;
  font-weight: 600;
  color: #1677ff;
}

.canvasContent {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.gridBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
  z-index: 1;
}

.emptyIcon {
  font-size: 48px;
  color: var(--tertiary-text);
  margin-bottom: 16px;
}

.emptyDescription {
  text-align: center;
}

.emptyDescription h3 {
  color: var(--primary-text);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.emptyDescription p {
  color: var(--secondary-text);
  font-size: 14px;
  margin: 4px 0;
  line-height: 1.5;
}

/* 暗色主题支持 */
[data-theme="dark"] .canvasContainer {
  background: #1a1a1a;
}

[data-theme="dark"] .canvasContainer.dragOver {
  background: rgba(22, 119, 255, 0.1);
  border-color: #4096ff;
}

[data-theme="dark"] .dropOverlay {
  background: rgba(22, 119, 255, 0.15);
}

[data-theme="dark"] .dropMessage {
  background: #2a2a2a;
  border-color: #4096ff;
}

[data-theme="dark"] .dropIcon {
  color: #4096ff;
}

[data-theme="dark"] .dropMessage span {
  color: #4096ff;
}

[data-theme="dark"] .gridBackground {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}
