.database-flow-container {
  width: 100%;
  height: 100vh;
  background-color: #f8f4f4;
}

.table-node {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  min-width: 150px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3 ease-out;
}

.table-node:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  /* transform: translateY(-2px); */
}

.table-header {
  position: relative;
  min-height: 24px;
  padding: 12px 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.table-name {
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--primary-text);
}

.table-content {
  padding: 8px;
  color: var(--secondary-text);
}

.column-row {
  position: relative;
  display: flex;
  align-items: center;
  padding: 6px 4px;
  gap: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.column-row:last-child {
  border-bottom: none;
}

.column-name {
  flex: 1;
  margin-left: 16px;
  font-weight: 500;
}

.column-info {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-left: auto;
}

.column-type {
  font-size: 0.8em;
  color: #666;
  background: var(--card-bg);
  padding: 2px 6px;
  border-radius: 4px;
}

.primary-key-badge {
  background: rgba(236, 125, 101, 0.604);
  color: var(--secondary-text);
  font-size: 0.7em;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: bold;
}

.foreign-key-badge {
  background: rgba(240, 173, 72, 0.74);
  color: var(--secondary-text);
  font-size: 0.7em;
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: bold;
}

/* 隐藏主键连接点 */
.hidden-primary-key-handle {
  opacity: 0 !important;
  pointer-events: none !important;
  width: 1px !important;
  height: 1px !important;
}

/* 保留外键连接点样式 */
.foreign-key-handle {
  width: 8px !important;
  height: 8px !important;
  background: #ff9900 !important;
  border: 2px solid white !important;
  transition:
    transform 0.2s ease,
    background-color 0.2s ease !important;
}

.foreign-key-handle:hover {
  transform: scale(1.5) !important;
  background: #ff7700 !important;
}

/* 背景样式 */
.react-flow__background {
  opacity: 0.5;
}

/* 恢复自定义缩放控件样式 */
.custom-zoom-controls {
  display: flex;
  gap: 8px;
  padding: 8px;
  /* background-color: var(--card-bg) !important; */
  background-color: transparent !important;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.zoom-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  color: var(--secondary-text);
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.zoom-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.zoom-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.fit-button {
  font-size: 14px;
}

/* 添加样式切换按钮样式 */
.style-button svg {
  width: 16px;
  height: 16px;
}

/* 约束图标样式 */
.constraint-icon {
  position: absolute;
  right: -24px;
  top: 50%;
  transform: translateY(-50%) translateX(0);

  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: rgb(92, 90, 90);
  font-size: 12px;
  cursor: help;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  opacity: 0;
}

.table-node:hover .constraint-icon {
  right: 8px;
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

.constraint-icon:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

/* 约束提示框样式 */
.constraint-tooltip-overlay {
  max-width: 300px !important;
}

.constraint-tooltip {
  padding: 8px 12px;
  font-size: 13px;
  line-height: 1.5;
}

.constraint-row {
  padding: 4px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.constraint-row:last-child {
  border-bottom: none;
}

.constraint-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.constraint-section:last-child {
  margin-bottom: 0;
}

.constraint-section h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.constraint-tooltip ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.constraint-tooltip li {
  padding: 4px 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.constraint-column {
  font-weight: 500;
  color: #1a1a1a;
}

.constraint-type {
  font-size: 0.85em;
  color: #666;
  font-family: monospace;
}

.foreign-key-refs {
  margin-top: 2px;
  padding-left: 12px;
}

.foreign-key-ref {
  font-size: 0.85em;
  color: #666;
  font-style: italic;
}

/* 确保提示框在节点上方显示 */
.ant-tooltip {
  z-index: 1000;
}

/* 暗色模式适配 */
[data-theme="dark"] .database-flow-container {
  background-color: rgb(83, 82, 80);
}

[data-theme="dark"] .table-node {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .table-header {
  background-color: var(--link-color);
}

[data-theme="dark"] .column-row {
  border-color: var(--divider-color);
}

[data-theme="dark"] .column-name {
  color: var(--primary-text);
}

[data-theme="dark"] .column-type {
  color: var(--tertiary-text);
}

[data-theme="dark"] .primary-key-icon {
  color: #ffb74d;
}

[data-theme="dark"] .foreign-key-icon {
  color: #64b5f6;
}

[data-theme="dark"] .react-flow__edge-path {
  stroke: var(--link-color);
}

[data-theme="dark"] .react-flow__node {
  color: var(--primary-text);
}

[data-theme="dark"] .react-flow__background {
  background-color: var(--background);
}

[data-theme="dark"] .react-flow__controls {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .react-flow__controls-button {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--primary-text);
}

[data-theme="dark"] .react-flow__controls-button:hover {
  background-color: var(--button-hover);
}

[data-theme="dark"] .custom-zoom-controls {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table-navigator {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .table-navigator-title {
  color: var(--primary-text);
  border-color: var(--divider-color);
}

[data-theme="dark"] .table-navigator-list {
  border-color: var(--divider-color);
}

[data-theme="dark"] .table-navigator-item {
  color: var(--secondary-text);
}

[data-theme="dark"] .table-navigator-item:hover {
  background-color: var(--button-hover);
}

[data-theme="dark"] .table-navigator-item.active {
  background-color: rgba(66, 133, 244, 0.2);
  color: var(--link-color);
}
