.guidingModal {
  max-width: 90vw;
}

.steps {
  margin-bottom: 24px;
}

.stepsContent {
  min-height: 300px;
  margin-top: 16px;
  padding: 20px;
  background-color: #fafafa;
  border: 1px dashed #e9e9e9;
  border-radius: 8px;
  position: relative; /* 添加相对定位 */
}

.stepContent {
  display: flex;
  flex-direction: column;
}

.stepContent h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.stepContent p {
  margin-bottom: 8px;
  color: #666;
}

.gifContainer {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  width: 100%; /* 添加宽度100% */
}

.videoPlayer {
  width: 100%;
  height: auto;
  border-radius: 8px;
  object-fit: cover; /* 改为cover */
  background: transparent; /* 添加透明背景 */
  display: block; /* 添加块级显示 */
  max-height: 400px; /* 添加最大高度限制 */
}

.placeholderImage {
  width: 100%;
  height: 200px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  border-radius: 4px;
}

.stepsAction {
  margin-top: 24px;
  display: flex;
  justify-content: center; /* 居中对齐 */
  align-items: center;
  gap: 16px; /* 按钮之间的间距 */
}

.stepsAction button {
  width: 30px; /* 固定按钮大小 */
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.stepsAction button:hover {
  transform: scale(1.05); /* 悬停时轻微放大效果 */
}
