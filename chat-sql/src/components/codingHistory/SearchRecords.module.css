.searchModal {
  background-color: var(--card-bg) !important;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .searchModal {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.searchInput {
  width: 100%;
  margin-bottom: 16px;
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border-color: var(--input-border) !important;
}

.searchInput input {
  color: var(--input-text) !important;
}

.searchInput:hover {
  border-color: var(--link-color) !important;
}

.searchInput:focus-within {
  border-color: var(--link-color) !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

[data-theme="dark"] .searchInput:focus-within {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
}

.resultList {
  max-height: 400px;
  overflow-y: auto;
}

.resultItem {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.resultItem:hover {
  background-color: var(--button-hover);
}

.resultTitle {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--primary-text);
}

.resultDate {
  font-size: 12px;
  color: var(--tertiary-text);
}

.noResults {
  text-align: center;
  padding: 20px;
  color: var(--tertiary-text);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--divider-color);
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-text);
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--tertiary-text);
  font-size: 20px;
}

.closeButton:hover {
  color: var(--primary-text);
}

.shortcutHint {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  color: var(--tertiary-text);
  font-size: 12px;
}

.keyboardKey {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 4px;
  margin: 0 4px;
  background-color: var(--button-hover);
  border-radius: 4px;
  border: 1px solid var(--card-border);
  color: var(--secondary-text);
  font-size: 11px;
  font-family: var(--font-mono);
}
